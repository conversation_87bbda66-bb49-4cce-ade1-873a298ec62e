// import the model
const Todo = require("../models/Todo.js");

// define route handler for get all todos-->
exports.getTodo = async(req , res)=>{
    try{
        const todos = await Todo.find({});
        res.status(200).json({
            success: true,
            data: todos,
            message: "Fetched all todos successfully"
        });
    }catch(err){
        console.error(err);
        res.status(500).json({
            success: false,
            data: null,
            message: "Server Error"
        });
    };
}
exports.getTodoById = async(req, res)=>{
    try{
        const id = req.params.id;
        const todo = await Todo.findById({_id: id});

        // if data is not found-->
        if(!todo){
            return res.status(404).json({
                success: false,
                data: null,
                message: "Todo not found"
            });
        }
        // id data is found-->
        res.status(200).json({
            success: true,
            data: todo,
            message: "Fetched todo successfully"
        })
    }
    catch(err){
        console.error(err);
        res.status(500).json({
            success: false,
            data: null,
            message: 'Server Error',
        })
    }
}