
const mongoose = require("mongoose");

// doing connection between node.js and your database-->
const dbConnect = () => {
    mongoose.connect(process.env.DATABASE_URL,{
        useNewUrlParser:true,
        useUnifiedtopology: true,
})
    .then(() =>console.log("DB is Success"))
    .catch((error) => {
        console.log("Issue in DB connection");
        console.error(error.message);
        process.exit(1);
    });
}

module.exports = dbConnect;
