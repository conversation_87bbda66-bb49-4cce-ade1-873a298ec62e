const Comment = require("../models/Comment.js");

exports.createComment = async (req,res) =>{
    try{
        const {postId, content} = req.body;
        console.log(postId, content);
        const comment = await Comment.create({postId, content});
        res.status(200).json({
            success: true,
            data: comment,
        });
    }
    catch(err){
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
}

exports.getComments = async (req,res) =>{
    try{
        const {postId} = req.params;
        const comments = await Comment.find({postId});
        res.status(200).json({
            success: true,
            data: comments
        });
    }
    catch(err){
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
}