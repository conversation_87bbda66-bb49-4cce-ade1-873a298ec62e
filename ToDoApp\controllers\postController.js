const Post = require("../models/Post.js");

exports.createPost = async(req, res) =>{
    try{
        // extract title and content from body
        const {title, content} = req.body;
        console.log(title, content);
        // create a new post obj and insert into db
        const post = await Post.create({title, content});
        // send a json response with a success flag
        res.status(200).json(
            {
                success: true,
                data: post,
                message:"comment posted successfully"
            }
        );
    }
    catch(err){
        console.error(err)
        console.log(err)
        res.status(500).json({
            success: false,
            data:"internal server error",
            message:err.message
        })
    }
};
exports.getPost = async(req, res) => {
    try{
        const posts = await Post.find({});
        res.status(200).json({
            success: true,
            data: posts
        });
    }
    catch(err){
        res.status(500).json({
            success:false,
            message: err.message
        });
    }
};
exports.likePost = async(req, res) => {
    try{
        const {postId , userId} = req.body
        const post = await Post.findById(postId);
        if(!post.likes.includes(userId)) post.likes.push(userId);
        await post.save();
        res.status(200).json({
            success: true,
            data: post,
            message:"Successfully like"
        });
    }
    catch(err){
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
};
exports.unlikePost = async(req, res) =>{
    try{
        const {postId , userId} = req.body;
        const post = await Post.findById(postId);
        post.likes = post.likes.filter(id => id !== userId);
        await post.save();
        res.status(200).json({
            success: true,
            data: post,
            message:"Find unlike post successfully"
        });
    }
    catch(err){
        res.status(500).json({
            success: false,
            message:err.message

        });
    }
}
