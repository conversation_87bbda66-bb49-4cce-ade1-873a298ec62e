const Todo = require("../models/Todo.js");

exports.updateTodo = async(req, res) => {
    try{
        const {id} = req.params;
        const{title, description} = req.body;

        const updateTodo = await Todo.findByIdAndUpdate(
            id,
            {   
                title, 
                description, 
                updatedAt: Date.now(),
            },
            {new: true}
        );
        if(!updateTodo){
            return res.status(400).json({
                success:false,
                message: "Todo not found",
            });
        }
        res.status(200).json({
        success: true,
        data:updateTodo,
        message: 'updated Successfully'
        })
    }
    catch(err){
       console.error("Update Error",err);
       res.status(500).json({
        success: false,
        error: err.message,
         message: 'Server Error',
       });
    }
    
}