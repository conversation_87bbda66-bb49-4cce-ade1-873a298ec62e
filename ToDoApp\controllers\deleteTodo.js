const Todo = require("../models/Todo.js");

exports.deleteTodo = async(req, res) => {
    try{
        const {id} = req.params;
        await Todo.findByIdAndDelete(id);
        res.json({
            success: true, 
            message:"Todo Deleted"
        })
    }
    catch(err){
        console.error("It's not working", err);
        res.status(500).json({
            success:false,
            error:err.message,
            message:"Server Error"
        });
    }
}



/*
define route for creating & retrieving posts
define route for liking & unliking post
define route for creating and retrieving comments
*/